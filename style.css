body {
    margin: 0;
    padding: 0;
    background: #000;
    font-family: 'Courier New', monospace;
    overflow: hidden;
    cursor: none;
}

.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#gameContainer {
    width: 100%;
    height: 100%;
}

.hud {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

.inventory {
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
}

.block-selector {
    display: flex;
    gap: 5px;
}

.block {
    width: 40px;
    height: 40px;
    border: 2px solid #fff;
    cursor: pointer;
    image-rendering: pixelated;
}

.block.grass { background: #7CB342; }
.block.stone { background: #757575; }
.block.wood { background: #8D6E63; }
.block.dirt { background: #5D4037; }

.block.active {
    border-color: #FFD700;
    box-shadow: 0 0 10px #FFD700;
}

.crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    font-weight: bold;
    z-index: 100;
    pointer-events: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    z-index: 10;
}