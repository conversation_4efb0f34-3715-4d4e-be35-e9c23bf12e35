body {
    margin: 0;
    padding: 0;
    background: #87CEEB;
    font-family: 'Courier New', monospace;
    overflow: hidden;
}

.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#gameCanvas {
    display: block;
    margin: 0 auto;
    border: 2px solid #333;
    cursor: crosshair;
}

.hud {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

.inventory {
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
}

.block-selector {
    display: flex;
    gap: 5px;
}

.block {
    width: 40px;
    height: 40px;
    border: 2px solid #fff;
    cursor: pointer;
    image-rendering: pixelated;
}

.block.grass { background: #7CB342; }
.block.stone { background: #757575; }
.block.wood { background: #8D6E63; }
.block.dirt { background: #5D4037; }

.block.active {
    border-color: #FFD700;
    box-shadow: 0 0 10px #FFD700;
}

.controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
}