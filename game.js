class Minecraft3D {
    constructor() {
        this.container = document.getElementById('gameContainer');
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.world = {};
        this.selectedBlock = 'grass';
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.isPointerLocked = false;

        // Player movement
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        this.moveSpeed = 50;
        this.jumpSpeed = 15;
        this.gravity = -30;
        this.onGround = false;

        // Raycaster for block interaction
        this.raycaster = new THREE.Raycaster();
        this.raycaster.far = 10;

        this.blockMaterials = {
            grass: new THREE.MeshLambertMaterial({ color: 0x7CB342 }),
            stone: new THREE.MeshLambertMaterial({ color: 0x757575 }),
            wood: new THREE.MeshLambertMaterial({ color: 0x8D6E63 }),
            dirt: new THREE.MeshLambertMaterial({ color: 0x5D4037 })
        };

        this.init();
    }

    init() {
        this.setupRenderer();
        this.setupLighting();
        this.setupCamera();
        this.generateTerrain();
        this.setupEventListeners();
        this.animate();
    }
    
    setupRenderer() {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
    }

    setupCamera() {
        this.camera.position.set(0, 20, 0);
        this.camera.lookAt(0, 19, -1);
    }

    generateTerrain() {
        // Generate simple terrain
        for (let x = -20; x < 20; x++) {
            for (let z = -20; z < 20; z++) {
                const height = Math.floor(Math.sin(x * 0.1) * Math.cos(z * 0.1) * 3) + 5;
                for (let y = 0; y <= height; y++) {
                    if (y === height) {
                        this.setBlock(x, y, z, 'grass');
                    } else if (y >= height - 2) {
                        this.setBlock(x, y, z, 'dirt');
                    } else {
                        this.setBlock(x, y, z, 'stone');
                    }
                }
            }
        }
    }
    
    setupEventListeners() {
        // Pointer lock
        this.renderer.domElement.addEventListener('click', () => {
            this.renderer.domElement.requestPointerLock();
        });

        document.addEventListener('pointerlockchange', () => {
            this.isPointerLocked = document.pointerLockElement === this.renderer.domElement;
        });

        // Mouse movement
        document.addEventListener('mousemove', (e) => {
            if (this.isPointerLocked) {
                this.mouse.x += e.movementX * 0.002;
                this.mouse.y += e.movementY * 0.002;
                this.mouse.y = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.mouse.y));

                this.camera.rotation.order = 'YXZ';
                this.camera.rotation.y = -this.mouse.x;
                this.camera.rotation.x = -this.mouse.y;
            }
        });

        // Mouse clicks
        document.addEventListener('mousedown', (e) => {
            if (this.isPointerLocked) {
                if (e.button === 0) { // Left click
                    this.placeBlock();
                }
            }
        });

        document.addEventListener('contextmenu', (e) => e.preventDefault());

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;

            // Block removal with E key
            if (e.code === 'KeyE' && this.isPointerLocked) {
                this.removeBlock();
            }

            // Number key block selection
            const blockTypes = ['grass', 'stone', 'wood', 'dirt'];
            if (e.code >= 'Digit1' && e.code <= 'Digit4') {
                const index = parseInt(e.code.slice(-1)) - 1;
                if (index < blockTypes.length) {
                    this.selectBlock(blockTypes[index]);
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });

        // Block selection
        document.querySelectorAll('.block').forEach(block => {
            block.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectBlock(block.dataset.type);
            });
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    placeBlock() {
        this.raycaster.setFromCamera({ x: 0, y: 0 }, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children);

        if (intersects.length > 0) {
            const intersect = intersects[0];
            const position = intersect.point.add(intersect.face.normal);
            const x = Math.floor(position.x);
            const y = Math.floor(position.y);
            const z = Math.floor(position.z);

            if (!this.getBlock(x, y, z)) {
                this.setBlock(x, y, z, this.selectedBlock);
            }
        }
    }

    removeBlock() {
        this.raycaster.setFromCamera({ x: 0, y: 0 }, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children);

        if (intersects.length > 0) {
            const intersect = intersects[0];
            const position = intersect.point.sub(intersect.face.normal.multiplyScalar(0.5));
            const x = Math.floor(position.x);
            const y = Math.floor(position.y);
            const z = Math.floor(position.z);

            this.removeBlockAt(x, y, z);
        }
    }

    setBlock(x, y, z, type) {
        const key = `${x},${y},${z}`;
        if (this.world[key]) return; // Block already exists

        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = this.blockMaterials[type];
        const mesh = new THREE.Mesh(geometry, material);

        mesh.position.set(x, y, z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = { x, y, z, type };

        this.scene.add(mesh);
        this.world[key] = mesh;
    }

    removeBlockAt(x, y, z) {
        const key = `${x},${y},${z}`;
        if (this.world[key]) {
            this.scene.remove(this.world[key]);
            delete this.world[key];
        }
    }

    getBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        return this.world[key];
    }

    selectBlock(blockType) {
        // Update visual selection
        document.querySelector('.block.active').classList.remove('active');
        document.querySelector(`.block[data-type="${blockType}"]`).classList.add('active');
        this.selectedBlock = blockType;
    }
    
    updateMovement(delta) {
        const speed = this.moveSpeed;

        this.direction.z = Number(this.keys['KeyW']) - Number(this.keys['KeyS']);
        this.direction.x = Number(this.keys['KeyA']) - Number(this.keys['KeyD']);
        this.direction.normalize();

        // Apply movement relative to camera direction
        const forward = new THREE.Vector3();
        this.camera.getWorldDirection(forward);
        forward.y = 0;
        forward.normalize();

        const right = new THREE.Vector3();
        right.crossVectors(forward, new THREE.Vector3(0, 1, 0));

        const moveVector = new THREE.Vector3();
        moveVector.addScaledVector(forward, this.direction.z);
        moveVector.addScaledVector(right, this.direction.x);

        this.velocity.x = moveVector.x * speed;
        this.velocity.z = moveVector.z * speed;

        // Jumping
        if (this.keys['Space'] && this.onGround) {
            this.velocity.y = this.jumpSpeed;
            this.onGround = false;
        }

        // Apply gravity
        this.velocity.y += this.gravity * delta;

        // Update position
        this.camera.position.addScaledVector(this.velocity, delta);

        // Simple ground collision (y = 10 is ground level)
        if (this.camera.position.y <= 10) {
            this.camera.position.y = 10;
            this.velocity.y = 0;
            this.onGround = true;
        }

        // Keep player within world bounds
        this.camera.position.x = Math.max(-50, Math.min(50, this.camera.position.x));
        this.camera.position.z = Math.max(-50, Math.min(50, this.camera.position.z));
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());

        const delta = 0.016; // Roughly 60 FPS

        if (this.isPointerLocked) {
            this.updateMovement(delta);
        }

        this.renderer.render(this.scene, this.camera);
    }
}

// Start the game
new Minecraft3D();