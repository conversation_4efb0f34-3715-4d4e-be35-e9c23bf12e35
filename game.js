class MinecraftGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.blockSize = 32;
        this.world = {};
        this.camera = { x: 0, y: 0 };
        this.selectedBlock = 'grass';
        this.keys = {};
        
        this.blockColors = {
            grass: '#7CB342',
            stone: '#757575',
            wood: '#8D6E63',
            dirt: '#5D4037'
        };
        
        this.init();
    }
    
    init() {
        this.generateTerrain();
        this.setupEventListeners();
        this.gameLoop();
    }
    
    generateTerrain() {
        // Generate simple terrain
        for (let x = -20; x < 20; x++) {
            const height = Math.floor(Math.sin(x * 0.1) * 3) + 5;
            for (let y = height; y < 15; y++) {
                if (y === height) {
                    this.setBlock(x, y, 'grass');
                } else if (y < height + 3) {
                    this.setBlock(x, y, 'dirt');
                } else {
                    this.setBlock(x, y, 'stone');
                }
            }
        }
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('click', (e) => this.handleClick(e, false));
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.handleClick(e, true);
        });
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.keys[e.key.toLowerCase()] = true);
        document.addEventListener('keyup', (e) => this.keys[e.key.toLowerCase()] = false);
        
        // Block selection
        document.querySelectorAll('.block').forEach(block => {
            block.addEventListener('click', () => {
                document.querySelector('.block.active').classList.remove('active');
                block.classList.add('active');
                this.selectedBlock = block.dataset.type;
            });
        });
    }
    
    handleClick(e, isRightClick) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const worldX = Math.floor((x + this.camera.x) / this.blockSize);
        const worldY = Math.floor((y + this.camera.y) / this.blockSize);
        
        if (isRightClick) {
            this.removeBlock(worldX, worldY);
        } else {
            this.setBlock(worldX, worldY, this.selectedBlock);
        }
    }
    
    setBlock(x, y, type) {
        if (!this.world[x]) this.world[x] = {};
        this.world[x][y] = type;
    }
    
    removeBlock(x, y) {
        if (this.world[x] && this.world[x][y]) {
            delete this.world[x][y];
        }
    }
    
    getBlock(x, y) {
        return this.world[x] && this.world[x][y];
    }
    
    updateCamera() {
        const speed = 5;
        if (this.keys['w']) this.camera.y -= speed;
        if (this.keys['s']) this.camera.y += speed;
        if (this.keys['a']) this.camera.x -= speed;
        if (this.keys['d']) this.camera.x += speed;
    }
    
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#87CEEB';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Calculate visible area
        const startX = Math.floor(this.camera.x / this.blockSize) - 1;
        const endX = startX + Math.ceil(this.canvas.width / this.blockSize) + 2;
        const startY = Math.floor(this.camera.y / this.blockSize) - 1;
        const endY = startY + Math.ceil(this.canvas.height / this.blockSize) + 2;
        
        // Render blocks
        for (let x = startX; x < endX; x++) {
            for (let y = startY; y < endY; y++) {
                const blockType = this.getBlock(x, y);
                if (blockType) {
                    const screenX = x * this.blockSize - this.camera.x;
                    const screenY = y * this.blockSize - this.camera.y;
                    
                    this.ctx.fillStyle = this.blockColors[blockType];
                    this.ctx.fillRect(screenX, screenY, this.blockSize, this.blockSize);
                    
                    // Block outline
                    this.ctx.strokeStyle = '#333';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(screenX, screenY, this.blockSize, this.blockSize);
                }
            }
        }
        
        // Render grid for empty spaces
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        for (let x = startX; x < endX; x++) {
            const screenX = x * this.blockSize - this.camera.x;
            this.ctx.beginPath();
            this.ctx.moveTo(screenX, 0);
            this.ctx.lineTo(screenX, this.canvas.height);
            this.ctx.stroke();
        }
        for (let y = startY; y < endY; y++) {
            const screenY = y * this.blockSize - this.camera.y;
            this.ctx.beginPath();
            this.ctx.moveTo(0, screenY);
            this.ctx.lineTo(this.canvas.width, screenY);
            this.ctx.stroke();
        }
    }
    
    gameLoop() {
        this.updateCamera();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Start the game
new MinecraftGame();